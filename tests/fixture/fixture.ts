import { test as base } from '@playwright/test';
import type { PlayWrightAiFixtureType } from '@midscene/web/playwright';
import { PlaywrightAiFixture } from '@midscene/web/playwright';
import { Assert } from './assertions';

// Define a new type that includes our custom assertion fixture
type MyFixtures = PlayWrightAiFixtureType & {
  assert: Assert;
};

// Extend the base test with both AI fixtures and our custom assertion fixture
export const test = base.extend<MyFixtures>({
  // Inherit the AI fixtures
  ...PlaywrightAiFixture(),

  // Add our custom assert fixture
  assert: async ({ page }, use) => {
    await use(new Assert(page));
  },
});