import { test as setup } from '../fixture/fixture';

const authFile = 'playwright/.auth/user.json';

setup('authenticate', async ({ page, ai, assert }) => {
  // 导航到登录页面
  await page.goto('https://www.laoyaoba.com/');

  // 执行登录操作
  await ai('点击"登录"');
  await ai('点击"账号密码登录"');
  await ai('在"手机号"输入框中输入"13261089257"');
  await ai('在"密码"输入框中输入"8131197.asd"');
  await ai('点击"登录"按钮');

  // 等待登录成功
  await page.waitForURL('https://www.laoyaoba.com/', { timeout: 15000 });
  // await assert.urlToContain('laoyaoba.com', 'URL should navigate to the opinion page');

  // 保存认证状态到文件
  await page.context().storageState({ path: authFile });
});
