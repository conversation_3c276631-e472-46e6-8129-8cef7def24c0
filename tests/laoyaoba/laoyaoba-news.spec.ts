import { test } from '../fixture/fixture';


test.beforeEach(async ({ page }) => {
  await page.goto('https://www.laoyaoba.com/');
});

test.describe('first Todo', () => { 
  test('get latest jwopinion from laoyaoba', async ({ page, ai, aiQuery, assert }) => {
    // 点击舆情，然后点击行业热点
    await ai('点击"舆情"');
    await assert.urlToContain('jwopinion', 'URL should navigate to the opinion page');
    await ai('点击"行业热点"');
    await assert.urlToContain('jwopinion/hot', 'URL should navigate to the hot news page');

    // 点击时间排序
    await ai('点击"时间排序"');
    await page.waitForLoadState('networkidle');

    // 返回最新一条数据的发布时间和来源
    const time = await aiQuery('string, 获取列表里第一条新闻的发布时间');
    const source = await aiQuery('string, 获取列表里第一条新闻的来源');

    console.log(`最新一条数据的发布时间: ${time}`);
    console.log(`最新一条数据的来源: ${source}`);

    // 使用封装的断言模块进行校验
    assert.isTruthy(time, '获取到的时间不应为空');
    assert.isTruthy(source, '获取到的来源不应为空');

  });

  test('get latest news from laoyaoba', async ({ page, ai, aiQuery, assert }) => {
    // 点击新闻
    await ai('点击"最新"');
    await assert.urlToContain('jwnews', 'URL should navigate to the news page');

    // 返回最新一条数据的发布时间和来源
    const time = await aiQuery('string, 获取列表里第一条新闻的发布时间');
    const source = await aiQuery('string, 获取列表里第一条新闻的来源');

    console.log(`最新一条数据的发布时间: ${time}`);
    console.log(`最新一条数据的来源: ${source}`);

    // 使用封装的断言模块进行校验
    assert.isTruthy(time, '获取到的时间不应为空');
    assert.isTruthy(source, '获取到的来源不应为空');

  });
});
